-- SQL script to create missing tables for the Aion Blitz website
-- Run this in phpMyAdmin if any tables are missing

-- Create donations table if it doesn't exist
CREATE TABLE IF NOT EXISTS `donations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `paypal_order_id` varchar(255) DEFAULT NULL,
  `status` enum('pending','completed','failed','cancelled') NOT NULL DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`),
  FOREIGN KEY (`user_id`) REFERENCES `account_data` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Create player_web_rewards table if it doesn't exist
CREATE TABLE IF NOT EXISTS `player_web_rewards` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` int(11) NOT NULL,
  `item_id` int(11) NOT NULL,
  `item_count` int(11) NOT NULL DEFAULT '1',
  `order_id` varchar(255) DEFAULT NULL,
  `added` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `received` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `player_id` (`player_id`),
  KEY `received` (`received`),
  FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Create account_stamps table if it doesn't exist
CREATE TABLE IF NOT EXISTS `account_stamps` (
  `account_id` int(11) NOT NULL,
  `stamps` int(11) NOT NULL DEFAULT '0',
  `last_stamp` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`account_id`),
  FOREIGN KEY (`account_id`) REFERENCES `account_data` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Create account_balance table if it doesn't exist
CREATE TABLE IF NOT EXISTS `account_balance` (
  `account_id` int(11) NOT NULL,
  `price_id` int(11) NOT NULL,
  `value` bigint(20) NOT NULL DEFAULT '0',
  PRIMARY KEY (`account_id`, `price_id`),
  KEY `price_id` (`price_id`),
  FOREIGN KEY (`account_id`) REFERENCES `account_data` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  FOREIGN KEY (`price_id`) REFERENCES `price_type` (`price_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Create price_type table if it doesn't exist
CREATE TABLE IF NOT EXISTS `price_type` (
  `price_id` int(11) NOT NULL AUTO_INCREMENT,
  `price_name` varchar(50) NOT NULL,
  `symbolic` varchar(10) NOT NULL,
  PRIMARY KEY (`price_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Insert default price types if they don't exist
INSERT IGNORE INTO `price_type` (`price_id`, `price_name`, `symbolic`) VALUES
(1, 'Might Points', 'MP'),
(2, 'Donation Points', 'DP'),
(3, 'Event Points', 'EP');

-- Add missing columns to account_data if they don't exist
ALTER TABLE `account_data` 
ADD COLUMN IF NOT EXISTS `creation_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP;

-- Add missing columns to abyss_rank if they don't exist  
ALTER TABLE `abyss_rank` 
ADD COLUMN IF NOT EXISTS `gp` int(11) NOT NULL DEFAULT '0' AFTER `glory`;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS `idx_account_data_name` ON `account_data` (`name`);
CREATE INDEX IF NOT EXISTS `idx_players_account_id` ON `players` (`account_id`);
CREATE INDEX IF NOT EXISTS `idx_players_online` ON `players` (`online`);
CREATE INDEX IF NOT EXISTS `idx_abyss_rank_player_id` ON `abyss_rank` (`player_id`);
