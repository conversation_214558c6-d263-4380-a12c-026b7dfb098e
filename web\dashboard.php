<?php
session_start();
require_once "db.php";

// Check if user is logged in
if (!isset($_SESSION["username"])) {
    header("Location: signin.php");
    exit;
}

$accountName = $_SESSION["username"];

// Initialize default values
$account = [
    'id' => 0,
    'name' => $accountName,
    'creation_date' => date('Y-m-d H:i:s'),
    'access_level' => 0,
    'membership' => 0,
    'last_active' => null,
    'accumulated_online' => 0,
    'session_duration' => 0
];

$playersResult = null;
$donationsResult = null;
$totalDonated = 0;
$webRewardsResult = null;
$stamps = ['stamps' => 0, 'last_stamp' => null];
$balances = [];

// Try to get account information with error handling
try {
    $accountQuery = "
        SELECT 
            ad.id,
            ad.name,
            COALESCE(ad.creation_date, NOW()) as creation_date,
            COALESCE(ad.access_level, 0) as access_level,
            COALESCE(ad.membership, 0) as membership,
            at.last_active,
            COALESCE(at.accumulated_online, 0) as accumulated_online,
            COALESCE(at.session_duration, 0) as session_duration
        FROM account_data ad
        LEFT JOIN account_time at ON ad.id = at.account_id
        WHERE ad.name = ?
    ";

    $stmt = $conn->prepare($accountQuery);
    if ($stmt) {
        $stmt->bind_param("s", $accountName);
        $stmt->execute();
        $accountResult = $stmt->get_result();
        $fetchedAccount = $accountResult->fetch_assoc();
        
        if ($fetchedAccount) {
            $account = $fetchedAccount;
        }
        $stmt->close();
    }
} catch (Exception $e) {
    error_log("Account query error: " . $e->getMessage());
}

// Try to get player characters with error handling
try {
    if ($account['id'] > 0) {
        $playersQuery = "
            SELECT 
                p.id,
                p.name,
                p.player_class,
                p.race,
                p.gender,
                COALESCE(p.exp, 0) as exp,
                COALESCE(p.online, 0) as online,
                p.last_online,
                p.creation_date,
                COALESCE(ar.gp, 0) as gp,
                COALESCE(ar.ap, 0) as ap,
                COALESCE(ar.all_kill, 0) as all_kill,
                COALESCE(ar.rank, 1) as rank
            FROM players p
            LEFT JOIN abyss_rank ar ON p.id = ar.player_id
            WHERE p.account_id = ?
            ORDER BY p.creation_date DESC
        ";

        $stmt = $conn->prepare($playersQuery);
        if ($stmt) {
            $stmt->bind_param("i", $account['id']);
            $stmt->execute();
            $playersResult = $stmt->get_result();
            $stmt->close();
        }
    }
} catch (Exception $e) {
    error_log("Players query error: " . $e->getMessage());
}

// Try to get donation history with error handling
try {
    if ($account['id'] > 0) {
        // Check if donations table exists
        $tableCheck = $conn->query("SHOW TABLES LIKE 'donations'");
        if ($tableCheck && $tableCheck->num_rows > 0) {
            $donationsQuery = "
                SELECT 
                    amount,
                    paypal_order_id,
                    status,
                    created_at
                FROM donations
                WHERE user_id = ?
                ORDER BY created_at DESC
                LIMIT 10
            ";

            $stmt = $conn->prepare($donationsQuery);
            if ($stmt) {
                $stmt->bind_param("i", $account['id']);
                $stmt->execute();
                $donationsResult = $stmt->get_result();
                $stmt->close();
            }

            // Calculate total donations
            $totalDonationsQuery = "
                SELECT SUM(amount) as total_donated
                FROM donations
                WHERE user_id = ? AND status = 'completed'
            ";

            $stmt = $conn->prepare($totalDonationsQuery);
            if ($stmt) {
                $stmt->bind_param("i", $account['id']);
                $stmt->execute();
                $totalDonationsResult = $stmt->get_result();
                $totalDonated = $totalDonationsResult->fetch_assoc()['total_donated'] ?? 0;
                $stmt->close();
            }
        }
    }
} catch (Exception $e) {
    error_log("Donations query error: " . $e->getMessage());
}

// Try to get pending web rewards with error handling
try {
    if ($account['id'] > 0) {
        // Check if player_web_rewards table exists
        $tableCheck = $conn->query("SHOW TABLES LIKE 'player_web_rewards'");
        if ($tableCheck && $tableCheck->num_rows > 0) {
            $webRewardsQuery = "
                SELECT 
                    pwr.item_id,
                    pwr.item_count,
                    pwr.added,
                    pwr.order_id
                FROM player_web_rewards pwr
                JOIN players p ON pwr.player_id = p.id
                WHERE p.account_id = ? AND pwr.received IS NULL
                ORDER BY pwr.added DESC
            ";

            $stmt = $conn->prepare($webRewardsQuery);
            if ($stmt) {
                $stmt->bind_param("i", $account['id']);
                $stmt->execute();
                $webRewardsResult = $stmt->get_result();
                $stmt->close();
            }
        }
    }
} catch (Exception $e) {
    error_log("Web rewards query error: " . $e->getMessage());
}

// Try to get account stamps with error handling
try {
    if ($account['id'] > 0) {
        // Check if account_stamps table exists
        $tableCheck = $conn->query("SHOW TABLES LIKE 'account_stamps'");
        if ($tableCheck && $tableCheck->num_rows > 0) {
            $stampsQuery = "
                SELECT stamps, last_stamp
                FROM account_stamps
                WHERE account_id = ?
            ";

            $stmt = $conn->prepare($stampsQuery);
            if ($stmt) {
                $stmt->bind_param("i", $account['id']);
                $stmt->execute();
                $stampsResult = $stmt->get_result();
                $fetchedStamps = $stampsResult->fetch_assoc();
                if ($fetchedStamps) {
                    $stamps = $fetchedStamps;
                }
                $stmt->close();
            }
        }
    }
} catch (Exception $e) {
    error_log("Stamps query error: " . $e->getMessage());
}

// Try to get account balance with error handling
try {
    if ($account['id'] > 0) {
        // Check if account_balance and price_type tables exist
        $tableCheck1 = $conn->query("SHOW TABLES LIKE 'account_balance'");
        $tableCheck2 = $conn->query("SHOW TABLES LIKE 'price_type'");
        
        if ($tableCheck1 && $tableCheck1->num_rows > 0 && $tableCheck2 && $tableCheck2->num_rows > 0) {
            $balanceQuery = "
                SELECT ab.value, pt.price_name, pt.symbolic
                FROM account_balance ab
                JOIN price_type pt ON ab.price_id = pt.price_id
                WHERE ab.account_id = ?
            ";

            $stmt = $conn->prepare($balanceQuery);
            if ($stmt) {
                $stmt->bind_param("i", $account['id']);
                $stmt->execute();
                $balanceResult = $stmt->get_result();
                while ($balance = $balanceResult->fetch_assoc()) {
                    $balances[] = $balance;
                }
                $stmt->close();
            }
        }
    }
} catch (Exception $e) {
    error_log("Balance query error: " . $e->getMessage());
}

// Helper functions
function calculateLevel($exp) {
    if ($exp < 1000) return 1;
    if ($exp < 5000) return 10;
    if ($exp < 50000) return 20;
    if ($exp < 200000) return 30;
    if ($exp < 500000) return 40;
    if ($exp < 1000000) return 50;
    if ($exp < 2000000) return 60;
    return 65;
}

function formatTime($seconds) {
    $hours = floor($seconds / 3600);
    $minutes = floor(($seconds % 3600) / 60);
    return "{$hours}h {$minutes}m";
}

function timeAgo($datetime) {
    if (!$datetime) return 'Never';
    $time = time() - strtotime($datetime);
    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    return date('M j, Y', strtotime($datetime));
}

// Calculate membership benefits
$membershipLevel = $account['membership'];
$membershipName = ['Free Player', 'Bronze Member', 'Silver Member', 'Gold Member', 'Platinum Member'][$membershipLevel] ?? 'Free Player';

$conn->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aion Blitz - Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f4f4f4; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: #333; color: white; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .card { background: white; padding: 20px; border-radius: 5px; margin-bottom: 20px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .status-online { color: #28a745; }
        .status-offline { color: #dc3545; }
        .btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px; display: inline-block; }
        .btn:hover { background: #0056b3; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Welcome to Aion Blitz Dashboard</h1>
            <p>Account: <?php echo htmlspecialchars($account['name']); ?> |
               Membership: <?php echo $membershipName; ?> |
               <a href="logout.php" style="color: #ffc107;">Logout</a></p>
        </div>

        <div class="grid">
            <div class="card">
                <h3>Account Information</h3>
                <p><strong>Account ID:</strong> <?php echo $account['id']; ?></p>
                <p><strong>Username:</strong> <?php echo htmlspecialchars($account['name']); ?></p>
                <p><strong>Access Level:</strong> <?php echo $account['access_level']; ?></p>
                <p><strong>Membership:</strong> <?php echo $membershipName; ?></p>
                <p><strong>Last Active:</strong> <?php echo timeAgo($account['last_active']); ?></p>
                <p><strong>Total Online Time:</strong> <?php echo formatTime($account['accumulated_online']); ?></p>
            </div>

            <div class="card">
                <h3>Account Balance</h3>
                <?php if (!empty($balances)): ?>
                    <?php foreach ($balances as $balance): ?>
                        <p><strong><?php echo htmlspecialchars($balance['price_name']); ?>:</strong>
                           <?php echo number_format($balance['value']); ?> <?php echo htmlspecialchars($balance['symbolic']); ?></p>
                    <?php endforeach; ?>
                <?php else: ?>
                    <p>No balance information available</p>
                <?php endif; ?>

                <?php if ($stamps['stamps'] > 0): ?>
                    <p><strong>Daily Stamps:</strong> <?php echo $stamps['stamps']; ?></p>
                    <p><strong>Last Stamp:</strong> <?php echo timeAgo($stamps['last_stamp']); ?></p>
                <?php endif; ?>
            </div>
        </div>

        <div class="card">
            <h3>Your Characters</h3>
            <?php if ($playersResult && $playersResult->num_rows > 0): ?>
                <table>
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Class</th>
                            <th>Race</th>
                            <th>Level</th>
                            <th>AP</th>
                            <th>GP</th>
                            <th>Kills</th>
                            <th>Status</th>
                            <th>Last Online</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while ($player = $playersResult->fetch_assoc()): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($player['name']); ?></td>
                                <td><?php echo htmlspecialchars($player['player_class']); ?></td>
                                <td><?php echo htmlspecialchars($player['race']); ?></td>
                                <td><?php echo calculateLevel($player['exp']); ?></td>
                                <td><?php echo number_format($player['ap']); ?></td>
                                <td><?php echo number_format($player['gp']); ?></td>
                                <td><?php echo number_format($player['all_kill']); ?></td>
                                <td class="<?php echo $player['online'] ? 'status-online' : 'status-offline'; ?>">
                                    <?php echo $player['online'] ? 'Online' : 'Offline'; ?>
                                </td>
                                <td><?php echo timeAgo($player['last_online']); ?></td>
                            </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <p>No characters found. Create your first character in-game!</p>
            <?php endif; ?>
        </div>

        <?php if ($webRewardsResult && $webRewardsResult->num_rows > 0): ?>
        <div class="card">
            <h3>Pending Web Rewards</h3>
            <table>
                <thead>
                    <tr>
                        <th>Item ID</th>
                        <th>Count</th>
                        <th>Order ID</th>
                        <th>Added</th>
                    </tr>
                </thead>
                <tbody>
                    <?php while ($reward = $webRewardsResult->fetch_assoc()): ?>
                        <tr>
                            <td><?php echo $reward['item_id']; ?></td>
                            <td><?php echo number_format($reward['item_count']); ?></td>
                            <td><?php echo htmlspecialchars($reward['order_id']); ?></td>
                            <td><?php echo timeAgo($reward['added']); ?></td>
                        </tr>
                    <?php endwhile; ?>
                </tbody>
            </table>
            <p><em>Login to the game to receive these rewards!</em></p>
        </div>
        <?php endif; ?>

        <?php if ($donationsResult && $donationsResult->num_rows > 0): ?>
        <div class="card">
            <h3>Donation History</h3>
            <p><strong>Total Donated:</strong> $<?php echo number_format($totalDonated, 2); ?></p>
            <table>
                <thead>
                    <tr>
                        <th>Amount</th>
                        <th>PayPal Order ID</th>
                        <th>Status</th>
                        <th>Date</th>
                    </tr>
                </thead>
                <tbody>
                    <?php while ($donation = $donationsResult->fetch_assoc()): ?>
                        <tr>
                            <td>$<?php echo number_format($donation['amount'], 2); ?></td>
                            <td><?php echo htmlspecialchars($donation['paypal_order_id']); ?></td>
                            <td><?php echo htmlspecialchars($donation['status']); ?></td>
                            <td><?php echo timeAgo($donation['created_at']); ?></td>
                        </tr>
                    <?php endwhile; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>

        <div class="card">
            <h3>Quick Actions</h3>
            <a href="change_password.php" class="btn">Change Password</a>
            <a href="download.php" class="btn">Download Client</a>
            <a href="support.php" class="btn">Support</a>
        </div>
    </div>
</body>
</html>
